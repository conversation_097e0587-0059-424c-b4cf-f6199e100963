import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';
import 'package:octasync_client/main.dart';

class PopupOverlayExamplePage extends StatefulWidget {
  const PopupOverlayExamplePage({super.key});

  @override
  State<PopupOverlayExamplePage> createState() => _PopupOverlayExamplePageState();
}

class _PopupOverlayExamplePageState extends State<PopupOverlayExamplePage> {
  // 控制器示例用
  late PopupOverlayController _popupController;

  @override
  void initState() {
    super.initState();
    _popupController = PopupOverlayController();
  }

  @override
  void dispose() {
    _popupController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('基础用法'),
          _buildBasicExample(),

          _buildSectionTitle('位置变体'),
          _buildPositionExamples(),

          _buildSectionTitle('控制器用法'),
          _buildControllerExample(),

          _buildSectionTitle('自定义样式'),
          _buildCustomStylingExample(),

          _buildSectionTitle('静态定位浮层'),
          _buildShowAtPositionExample(),

          _buildSectionTitle('应用场景示例'),
          _buildApplicationExamples(),
        ],
      ),
    );
  }

  // 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 16),
      child: Text(title, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
    );
  }

  // 构建示例容器
  Widget _buildExampleContainer({required Widget child, double? height}) {
    return Container(
      width: double.infinity,
      height: height,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: context.border200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: child,
    );
  }

  // 下面将实现各个示例部分
  Widget _buildBasicExample() {
    return _buildExampleContainer(
      height: 200,
      child: Center(
        child: PopupOverlay(
          navigatorKey: navigatorKey,
          child: AppButton(text: '点击显示浮层', type: ButtonType.primary),
          builder: (context, closeOverlay) {
            return Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('基础浮层', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  const Text('这是一个基础的弹出浮层，点击外部区域可关闭'),
                  const SizedBox(height: 12),
                  AppButton(text: '关闭浮层', type: ButtonType.primary, onPressed: closeOverlay),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPositionExamples() {
    return Column(
      children: [
        Text('浮层可以设置从不同方向弹出。通过position属性控制浮层出现的位置。'),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPositionExample(label: '上方弹出', position: PopupPosition.top),
            _buildPositionExample(label: '右侧弹出', position: PopupPosition.right),
            _buildPositionExample(label: '下方弹出', position: PopupPosition.bottom),
            _buildPositionExample(label: '左侧弹出', position: PopupPosition.left),
          ],
        ),
      ],
    );
  }

  // 不同位置弹出浮层示例
  Widget _buildPositionExample({required String label, required PopupPosition position}) {
    return Column(
      children: [
        Text(label, style: const TextStyle(fontSize: 12)),
        const SizedBox(height: 8),
        PopupOverlay(
          navigatorKey: navigatorKey,
          position: position,
          child: AppButton(
            iconData: Icons.arrow_drop_down,
            type: ButtonType.primary,
            size: ButtonSize.small,
          ),
          builder: (context, closeOverlay) {
            return Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('$label 示例', style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  const Text('这是一个弹出浮层示例'),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildControllerExample() {
    return _buildExampleContainer(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('通过控制器可以在外部控制浮层的显示和隐藏', textAlign: TextAlign.center),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                PopupOverlay(
                  navigatorKey: navigatorKey,
                  controller: _popupController,
                  child: AppButton(text: '浮层锚点', type: ButtonType.primary),
                  builder: (context, closeOverlay) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('通过控制器操作的浮层', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          const Text('这个浮层通过外部按钮控制显示和隐藏'),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton(onPressed: closeOverlay, child: const Text('关闭')),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(width: 20),
                Column(
                  children: [
                    AppButton(
                      text: '显示浮层',
                      type: ButtonType.primary,
                      onPressed: () => _popupController.show(),
                    ),
                    const SizedBox(height: 8),
                    AppButton(
                      text: '隐藏浮层',
                      type: ButtonType.primary,
                      onPressed: () => _popupController.hide(),
                    ),
                    const SizedBox(height: 8),
                    AppButton(
                      text: '切换显示',
                      type: ButtonType.primary,
                      onPressed: () => _popupController.toggle(),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomStylingExample() {
    return _buildExampleContainer(
      height: 300,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('可以自定义浮层的样式，包括背景色、边框、圆角等', textAlign: TextAlign.center),
            const SizedBox(height: 20),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              alignment: WrapAlignment.center,
              children: [
                _buildStyledPopup(
                  label: '自定义背景色',
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                ),
                _buildStyledPopup(label: '自定义边框', borderColor: Colors.red, borderWidth: 2),
                _buildStyledPopup(label: '自定义圆角', borderRadius: BorderRadius.circular(16)),
                _buildStyledPopup(label: '无阴影效果', elevation: 0),
                _buildStyledPopup(label: '强阴影效果', elevation: 24),
                _buildStyledPopup(label: '自定义偏移', offset: const Offset(20, 20)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 自定义样式浮层示例
  Widget _buildStyledPopup({
    required String label,
    Color? backgroundColor,
    Color? textColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    double? elevation,
    Offset? offset,
  }) {
    return PopupOverlay(
      navigatorKey: navigatorKey,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth ?? 1.0,
      borderRadius: borderRadius ?? BorderRadius.circular(6),
      elevation: elevation ?? 8,
      offset: offset ?? Offset.zero,
      child: AppButton(text: label, type: ButtonType.primary),
      builder: (context, closeOverlay) {
        return Container(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: TextStyle(fontWeight: FontWeight.bold, color: textColor)),
              const SizedBox(height: 8),
              Text('这是一个自定义样式的浮层示例', style: TextStyle(color: textColor)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShowAtPositionExample() {
    return _buildExampleContainer(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('通过showAtPosition静态方法可以在屏幕的任意位置显示浮层', textAlign: TextAlign.center),
            const SizedBox(height: 20),
            AppButton(
              text: '在随机位置显示浮层',
              type: ButtonType.primary,
              onPressed: () {
                // 在点击位置附近显示浮层
                _showAtRandomPosition(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  // 在随机位置显示浮层
  void _showAtRandomPosition(BuildContext context) {
    // 获取屏幕尺寸
    final size = MediaQuery.of(context).size;

    // 模拟随机位置，实际上是固定中间偏右上
    final position = Offset(size.width * 0.6, size.height * 0.4);

    PopupOverlay.showAtPosition(
      context: context,
      position: position,
      builder: (context, close) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('定位浮层', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              const Text('这个浮层可以显示在屏幕的任意位置'),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [TextButton(onPressed: close, child: const Text('关闭'))],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildApplicationExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('下面是一些弹出浮层的实际应用场景示例:'),
        const SizedBox(height: 20),

        // 下拉菜单示例
        _buildApplicationExample(title: '下拉菜单', child: _buildDropdownMenuExample()),

        const SizedBox(height: 20),

        // 上下文操作菜单示例
        _buildApplicationExample(title: '上下文操作菜单', child: _buildContextMenuExample()),

        const SizedBox(height: 20),

        // 信息提示示例
        _buildApplicationExample(title: '信息提示', child: _buildInfoTooltipExample()),
      ],
    );
  }

  // 应用场景示例容器
  Widget _buildApplicationExample({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16)),
        const SizedBox(height: 8),
        _buildExampleContainer(height: 120, child: Center(child: child)),
      ],
    );
  }

  // 下拉菜单示例
  Widget _buildDropdownMenuExample() {
    return PopupOverlay(
      navigatorKey: navigatorKey,
      child: AppButton(text: '菜单', iconData: Icons.menu, type: ButtonType.primary),
      builder: (context, closeOverlay) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMenuItem(
              icon: Icons.edit,
              text: '编辑',
              onTap: () {
                // 处理编辑操作
                closeOverlay();
              },
            ),
            _buildMenuItem(
              icon: Icons.copy,
              text: '复制',
              onTap: () {
                // 处理复制操作
                closeOverlay();
              },
            ),
            _buildMenuItem(
              icon: Icons.delete,
              text: '删除',
              onTap: () {
                // 处理删除操作
                closeOverlay();
              },
            ),
            _buildMenuItem(
              icon: Icons.share,
              text: '分享',
              onTap: () {
                // 处理分享操作
                closeOverlay();
              },
            ),
          ],
        );
      },
    );
  }

  // 菜单项
  Widget _buildMenuItem({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(children: [Icon(icon, size: 20), const SizedBox(width: 12), Text(text)]),
      ),
    );
  }

  // 上下文操作菜单示例
  Widget _buildContextMenuExample() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: context.border200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.insert_drive_file),
          const SizedBox(width: 8),
          const Text('文档.docx'),
          const SizedBox(width: 16),
          PopupOverlay(
            navigatorKey: navigatorKey,
            position: PopupPosition.bottom,
            constraints: BoxConstraints(minHeight: 100, maxHeight: 200),
            child: AppButton(
              iconData: Icons.more_vert,
              size: ButtonSize.medium,
              type: ButtonType.transparent,
              color: AppColors.primary,
            ),
            builder: (context, closeOverlay) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMenuItem(icon: Icons.remove_red_eye, text: '查看', onTap: closeOverlay),
                  _buildMenuItem(icon: Icons.download, text: '下载', onTap: closeOverlay),
                  _buildMenuItem(icon: Icons.delete, text: '删除', onTap: closeOverlay),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // 信息提示示例
  Widget _buildInfoTooltipExample() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('带有详细信息的表单项'),
        const SizedBox(width: 8),
        PopupOverlay(
          navigatorKey: navigatorKey,
          position: PopupPosition.top,
          child: const Icon(Icons.info_outline, size: 20, color: Colors.blue),
          builder: (context, closeOverlay) {
            return Container(
              padding: const EdgeInsets.all(12),
              constraints: const BoxConstraints(maxWidth: 200),
              child: const Text('这里是关于该表单项的详细说明，可以包含使用指南、注意事项等信息。', style: TextStyle(fontSize: 12)),
            );
          },
        ),
      ],
    );
  }
}
