import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/imports.dart';

class DepartmentSelector extends StatefulWidget {
  const DepartmentSelector({super.key});

  @override
  State<DepartmentSelector> createState() => _DepartmentSelectorState();
}

class _DepartmentSelectorState extends State<DepartmentSelector> {
  final reqParams = {'PageIndex': 1, 'PageSize': 9999};
  var list = [];

  @override
  void initState() {
    super.initState();

    getList();
  }

  void getList() {
    DepartmentApi.getList(reqParams).then((value) {
      setState(() {
        list = value['Items'];
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: context.border300, width: 1)),
      ),
      child: Column(children: list.map((item) => Text(item['DepartmentName'])).toList()),
    );
  }
}
