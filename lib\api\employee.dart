import 'package:flutter/material.dart';
import 'package:octasync_client/utils/http_service.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/providers/user_provider.dart';
import 'package:provider/provider.dart';

final _http = HttpService();

class EmployeeApi {
  /// 登录
  static Future<dynamic> login(data) {
    return _http.post('/Business/Employee/Login', data: data);
  }

  /// 退出登录
  static Future<void> logOut(BuildContext context) async {
    final router = GoRouter.of(context);
    await Provider.of<UserProvider>(context, listen: false).clearUserInfo();
    router.go('/login');
  }
}
