import 'package:flutter/material.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/main.dart';
import 'package:octasync_client/providers/theme_provider.dart';
import 'package:provider/provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class CollaborationPage extends StatelessWidget {
  const CollaborationPage({super.key});

  @override
  Widget build(BuildContext context) {
    List<UserInfo> employeeList = [
      UserInfo(
        avatar:
            'http://192.168.99.100:8082/Files/Images/202405/e2ac63a9467e2dba481b42a5a94e3714/5816f4da5fc49.jpg',
        name: '名字名字名字',
      ),
      UserInfo(avatar: ''),
      UserInfo(avatar: ''),
      UserInfo(avatar: ''),
      UserInfo(avatar: ''),
    ];

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            PopupOverlay(
              navigatorKey: navigator<PERSON>ey,
              child: AppButton(text: '打开浮层', type: ButtonType.primary),
              builder: (context, closeOverlay) {
                return Column(children: [...List.generate(100, (index) => Text(index.toString()))]);
              },
            ),
            const SizedBox(height: 20),
            AppAvatar(users: employeeList),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                final themeProvider = context.read<ThemeProvider>();
                themeProvider.toggleTheme();
              },
              child: const Text('主题切换'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(onPressed: () => EmployeeApi.logOut(context), child: const Text('退出登录')),
            const SizedBox(height: 20),
            ElevatedButton(onPressed: () => StorageUtil.clear(), child: const Text('清除Storage缓存')),
            const SizedBox(height: 20),
            ElevatedButton(onPressed: () => ImageCacheUtil.clear(), child: const Text('清除图片缓存')),
          ],
        ),
      ),
    );
  }
}
