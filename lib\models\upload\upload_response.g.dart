// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadResponse _$UploadResponseFromJson(Map<String, dynamic> json) => UploadResponse(
  filePath: json['FilePath'] as String?,
  path: json['Path'] as String?,
  poster: json['Poster'],
  urlThum: json['UrlThum'],
  fileSize: (json['FileSize'] as num?)?.toInt(),
  fileName: json['FileName'] as String?,
  fileMd5value: json['FileMd5value'] as String?,
  fileId: json['FileId'] as String?,
  contentType: json['ContentType'],
);

Map<String, dynamic> _$UploadResponseToJson(UploadResponse instance) => <String, dynamic>{
  'FilePath': instance.filePath,
  'Path': instance.path,
  'Poster': instance.poster,
  'UrlThum': instance.urlThum,
  'FileSize': instance.fileSize,
  'FileName': instance.fileName,
  'FileMd5value': instance.fileMd5value,
  'FileId': instance.fileId,
  'ContentType': instance.contentType,
};
